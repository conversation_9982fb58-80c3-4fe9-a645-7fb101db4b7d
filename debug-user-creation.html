<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户调试工具</h1>
        <p>这个工具用于调试用户认证问题和创建测试用户。</p>
        
        <div id="status"></div>
        
        <h3>操作</h3>
        <button class="button" onclick="checkUsers()">检查现有用户</button>
        <button class="button" onclick="createTestUser()">创建测试用户</button>
        <button class="button" onclick="testLogin()">测试登录</button>
        
        <h3>创建自定义用户</h3>
        <input type="email" id="userEmail" placeholder="邮箱" value="<EMAIL>">
        <input type="text" id="userName" placeholder="姓名" value="测试用户">
        <input type="password" id="userPassword" placeholder="密码" value="123456">
        <button class="button" onclick="createCustomUser()">创建用户</button>
        
        <div id="results"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/supabase-simple.js"></script>
    <script src="js/auth-system.js"></script>

    <script>
        function showMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        async function checkUsers() {
            try {
                showMessage('正在检查用户...', 'info');
                
                const supabaseClient = window.supabaseClient || window.supabase?.createClient?.(
                    'https://snckktsqwrbfwtjlvcfr.supabase.co',
                    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNuY2trdHNxd3JiZnd0amx2Y2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExMDAwMDksImV4cCI6MjA2NjY3NjAwOX0.L83td9BHYz7Z6vQke7CXPZrcOXcQ8FKg9duBL-fm644'
                );

                const { data, error } = await supabaseClient
                    .from('users')
                    .select('id, email, name, username, user_type, created_at')
                    .limit(10);

                if (error) {
                    showMessage('查询用户失败: ' + error.message, 'error');
                    showResults(error);
                } else {
                    showMessage(`找到 ${data.length} 个用户`, 'success');
                    showResults(data);
                }
            } catch (error) {
                showMessage('检查用户时出错: ' + error.message, 'error');
                console.error(error);
            }
        }

        async function createTestUser() {
            try {
                showMessage('正在创建测试用户...', 'info');
                
                const userData = {
                    email: '<EMAIL>',
                    name: '测试用户',
                    password: '123456',
                    user_type: 'premium'
                };

                const result = await window.authSystem.registerUser(userData);
                
                if (result.success) {
                    showMessage('测试用户创建成功！', 'success');
                    showResults(result.user);
                } else {
                    showMessage('创建测试用户失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('创建测试用户时出错: ' + error.message, 'error');
                console.error(error);
            }
        }

        async function createCustomUser() {
            try {
                const email = document.getElementById('userEmail').value;
                const name = document.getElementById('userName').value;
                const password = document.getElementById('userPassword').value;

                if (!email || !name || !password) {
                    showMessage('请填写所有字段', 'error');
                    return;
                }

                showMessage('正在创建用户...', 'info');
                
                const userData = {
                    email: email,
                    name: name,
                    password: password,
                    user_type: 'premium'
                };

                const result = await window.authSystem.registerUser(userData);
                
                if (result.success) {
                    showMessage('用户创建成功！', 'success');
                    showResults(result.user);
                } else {
                    showMessage('创建用户失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('创建用户时出错: ' + error.message, 'error');
                console.error(error);
            }
        }

        async function testLogin() {
            try {
                showMessage('正在测试登录...', 'info');
                
                const result = await window.authSystem.loginUser('<EMAIL>', '123456');
                
                if (result.success) {
                    showMessage('登录测试成功！', 'success');
                    showResults(result.user);
                } else {
                    showMessage('登录测试失败', 'error');
                }
            } catch (error) {
                showMessage('登录测试时出错: ' + error.message, 'error');
                console.error(error);
            }
        }

        // 页面加载时检查系统状态
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.supabaseClient) {
                    showMessage('Supabase客户端已加载', 'success');
                } else {
                    showMessage('Supabase客户端未加载', 'error');
                }
                
                if (window.authSystem) {
                    showMessage('认证系统已加载', 'success');
                } else {
                    showMessage('认证系统未加载', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>

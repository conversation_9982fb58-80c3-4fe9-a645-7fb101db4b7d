// 统一的Supabase认证系统
class AuthSystem {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.userType = 'guest';
        
        // 用户权限类型
        this.USER_TYPES = {
            GUEST: 'guest',           // 游客 - 基本浏览权限
            PREMIUM: 'premium',       // 高级用户 - 注册用户，可查看详情
            PRIVILEGED: 'privileged', // 特许用户 - 可下载部分资料
            ADMIN: 'admin'            // 管理员 - 完全权限
        };
        
        this.init();
    }
    
    async init() {
        console.log('🔐 [AUTH-SYSTEM] 开始初始化认证系统');
        
        // 等待Supabase客户端初始化
        await this.waitForSupabase();
        
        // 检查登录状态
        await this.checkLoginStatus();
        
        console.log('🔐 [AUTH-SYSTEM] 认证系统初始化完成');
    }
    
    // 等待Supabase客户端初始化
    async waitForSupabase() {
        let retryCount = 0;
        while (retryCount < 20) {
            if (window.supabaseClient) {
                this.supabase = window.supabaseClient;
                console.log('🔐 [AUTH-SYSTEM] Supabase客户端已连接');
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 200));
            retryCount++;
        }
        console.error('🔐 [AUTH-SYSTEM] Supabase客户端初始化超时');
    }
    
    // 检查登录状态
    async checkLoginStatus() {
        try {
            // 从本地存储检查登录状态
            const savedUser = localStorage.getItem('simple_auth_user');
            const loginTime = localStorage.getItem('simple_auth_login_time');
            
            if (savedUser && loginTime) {
                const user = JSON.parse(savedUser);
                const loginTimestamp = parseInt(loginTime);
                const currentTime = Date.now();
                const sessionDuration = 24 * 60 * 60 * 1000; // 24小时
                
                if (currentTime - loginTimestamp < sessionDuration) {
                    this.currentUser = user;
                    this.userType = user.user_type || this.USER_TYPES.PREMIUM;
                    this.updateGlobalState();
                    this.updateUI();
                    console.log('🔐 [AUTH-SYSTEM] 恢复登录状态:', user.email);
                    return true;
                } else {
                    // 登录已过期
                    this.logout();
                    console.log('🔐 [AUTH-SYSTEM] 登录已过期，已清除');
                }
            }
        } catch (error) {
            console.error('🔐 [AUTH-SYSTEM] 检查登录状态失败:', error);
        }
        return false;
    }
    
    // 密码哈希函数
    hashPassword(password) {
        return btoa(password + 'chunsheng_salt');
    }
    
    // 验证密码
    verifyPassword(password, hashedPassword) {
        return this.hashPassword(password) === hashedPassword;
    }
    
    // 用户登录
    async loginUser(email, password) {
        console.log('🔐 [AUTH-SYSTEM] 开始登录流程:', email);
        
        if (!this.supabase) {
            throw new Error('Supabase客户端未初始化');
        }
        
        try {
            // 查询用户
            const { data: users, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('email', email.toLowerCase())
                .limit(1);
            
            if (error) {
                console.error('🔐 [AUTH-SYSTEM] 查询用户失败:', error);
                throw new Error('登录失败，请稍后重试');
            }
            
            if (!users || users.length === 0) {
                throw new Error('用户不存在');
            }
            
            const user = users[0];
            
            // 验证密码
            if (!this.verifyPassword(password, user.password)) {
                throw new Error('密码错误');
            }
            
            // 登录成功
            this.currentUser = user;
            this.userType = user.user_type || this.USER_TYPES.PREMIUM;
            
            // 保存登录状态
            localStorage.setItem('simple_auth_user', JSON.stringify(user));
            localStorage.setItem('simple_auth_login_time', Date.now().toString());
            
            this.updateGlobalState();
            this.updateUI();
            
            console.log('🔐 [AUTH-SYSTEM] 登录成功:', user.email);
            return { success: true, user: user };
            
        } catch (error) {
            console.error('🔐 [AUTH-SYSTEM] 登录失败:', error);
            throw error;
        }
    }
    
    // 用户注册
    async registerUser(userData) {
        console.log('🔐 [AUTH-SYSTEM] 开始注册流程:', userData.email);
        
        if (!this.supabase) {
            throw new Error('Supabase客户端未初始化');
        }
        
        try {
            // 检查用户是否已存在
            const { data: existingUsers, error: checkError } = await this.supabase
                .from('users')
                .select('email')
                .eq('email', userData.email.toLowerCase())
                .limit(1);
            
            if (checkError) {
                console.error('🔐 [AUTH-SYSTEM] 检查用户失败:', checkError);
                throw new Error('注册失败，请稍后重试');
            }
            
            if (existingUsers && existingUsers.length > 0) {
                throw new Error('该邮箱已被注册');
            }
            
            // 创建新用户
            const newUser = {
                email: userData.email.toLowerCase(),
                password: this.hashPassword(userData.password),
                name: userData.name || '',
                user_type: userData.user_type || this.USER_TYPES.PREMIUM,
                created_at: new Date().toISOString()
            };
            
            const { data, error } = await this.supabase
                .from('users')
                .insert([newUser])
                .select()
                .single();
            
            if (error) {
                console.error('🔐 [AUTH-SYSTEM] 注册失败:', error);
                throw new Error('注册失败，请稍后重试');
            }
            
            console.log('🔐 [AUTH-SYSTEM] 注册成功:', data.email);
            return { success: true, user: data };
            
        } catch (error) {
            console.error('🔐 [AUTH-SYSTEM] 注册失败:', error);
            throw error;
        }
    }
    
    // 用户登出
    logout() {
        console.log('🔐 [AUTH-SYSTEM] 用户登出');
        
        this.currentUser = null;
        this.userType = this.USER_TYPES.GUEST;
        
        // 清除本地存储
        localStorage.removeItem('simple_auth_user');
        localStorage.removeItem('simple_auth_login_time');
        
        this.updateGlobalState();
        this.updateUI();
    }
    
    // 更新全局状态
    updateGlobalState() {
        window.currentUser = this.currentUser;
        window.currentUserType = this.userType;
        window.USER_TYPES = this.USER_TYPES;
    }
    
    // 更新UI
    updateUI() {
        // 触发UI更新事件
        window.dispatchEvent(new CustomEvent('authStateChanged', {
            detail: {
                user: this.currentUser,
                userType: this.userType
            }
        }));
    }
    
    // 权限检查方法
    isLoggedIn() {
        return this.currentUser !== null;
    }
    
    isAdmin() {
        return this.userType === this.USER_TYPES.ADMIN;
    }
    
    canViewDetails() {
        return this.userType !== this.USER_TYPES.GUEST;
    }
    
    canDownloadBasic() {
        return this.userType === this.USER_TYPES.PRIVILEGED || this.userType === this.USER_TYPES.ADMIN;
    }
    
    canDownloadAll() {
        return this.userType === this.USER_TYPES.ADMIN;
    }
    
    getCurrentUser() {
        return this.currentUser;
    }
    
    getUserType() {
        return this.userType;
    }
}

// 创建全局认证系统实例
window.authSystem = new AuthSystem();

// 兼容性函数（保持旧版API）
window.loginUser = (email, password) => window.authSystem.loginUser(email, password);
window.registerUser = (userData) => window.authSystem.registerUser(userData);
window.logoutUser = () => window.authSystem.logout();
window.getCurrentUserInfo = () => window.authSystem.checkLoginStatus();
window.checkAdminAuth = () => window.authSystem.isAdmin();

// 权限检查全局函数
window.canViewDetails = () => window.authSystem.canViewDetails();
window.canDownload = () => window.authSystem.canDownloadBasic();
window.canDownloadBasic = () => window.authSystem.canDownloadBasic();
window.canDownloadAll = () => window.authSystem.canDownloadAll();

// 初始化全局变量
window.currentUser = null;
window.currentUserType = 'guest';
window.USER_TYPES = {
    GUEST: 'guest',
    PREMIUM: 'premium',
    PRIVILEGED: 'privileged',
    ADMIN: 'admin'
};

console.log('🔐 统一认证系统已加载');
